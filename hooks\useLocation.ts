/**
 * Location Hook
 * React hook for managing location state and operations
 * Follows React hooks best practices and separation of concerns
 */

import { useCallback, useEffect, useRef, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { locationService } from '../services/location';
import {
  ILocationService,
  LocationData,
  LocationError,
  LocationPermissionStatus,
  LocationResult
} from '../types/location';

interface UseLocationOptions {
  autoFetch?: boolean;
  fetchOnAppFocus?: boolean;
  service?: ILocationService;
}

interface UseLocationReturn {
  // State
  location: LocationData | null;
  lastKnownLocation: LocationData | null;
  error: LocationError | null;
  isLoading: boolean;
  permissionStatus: LocationPermissionStatus | null;

  // Actions
  getCurrentLocation: () => Promise<void>;
  getLastKnownLocation: () => Promise<void>;
  requestPermissions: () => Promise<void>;
  clearError: () => void;

  // Computed
  hasPermission: boolean;
  canRequestPermission: boolean;
}

export function useLocation(options: UseLocationOptions = {}): UseLocationReturn {
  const {
    autoFetch = false,
    fetchOnAppFocus = true,
    service = locationService,
  } = options;

  // State
  const [location, setLocation] = useState<LocationData | null>(null);
  const [lastKnownLocation, setLastKnownLocation] = useState<LocationData | null>(null);
  const [error, setError] = useState<LocationError | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [permissionStatus, setPermissionStatus] = useState<LocationPermissionStatus | null>(null);

  // Refs
  const appStateRef = useRef<AppStateStatus>(AppState.currentState);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Handle location result
  const handleLocationResult = useCallback((result: LocationResult, isLastKnown = false) => {
    if (result.success) {
      if (isLastKnown) {
        setLastKnownLocation(result.data);
      } else {
        setLocation(result.data);
      }
      setError(null);
    } else {
      setError(result.error);
    }
  }, []);

  // Get current location
  const getCurrentLocation = useCallback(async () => {
    if (isLoading) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await service.getCurrentLocation();
      handleLocationResult(result);
    } catch (err) {
      console.error('Hook: getCurrentLocation error:', err);
      setError({
        type: 'UNKNOWN_ERROR' as any,
        message: 'Konum alınırken beklenmeyen hata',
        originalError: err instanceof Error ? err : new Error(String(err)),
      });
    } finally {
      setIsLoading(false);
    }
  }, [service, handleLocationResult, isLoading]);

  // Get last known location
  const getLastKnownLocation = useCallback(async () => {
    try {
      const result = await service.getLastKnownLocation();
      handleLocationResult(result, true);
    } catch (err) {
      console.error('Hook: getLastKnownLocation error:', err);
    }
  }, [service, handleLocationResult]);

  // Request permissions
  const requestPermissions = useCallback(async () => {
    try {
      const status = await service.requestPermissions();
      setPermissionStatus(status);

      // If permission granted, try to get location
      if (status.granted && autoFetch) {
        // Call service directly to avoid circular dependency
        setIsLoading(true);
        try {
          const result = await service.getCurrentLocation();
          handleLocationResult(result);
        } catch (locationErr) {
          console.error('Hook: auto-fetch location error:', locationErr);
        } finally {
          setIsLoading(false);
        }
      }
    } catch (err) {
      console.error('Hook: requestPermissions error:', err);
      setError({
        type: 'PERMISSION_DENIED' as any,
        message: 'İzin isteme işlemi başarısız',
        originalError: err instanceof Error ? err : new Error(String(err)),
      });
    }
  }, [service, autoFetch, handleLocationResult]);

  // Check permissions on mount
  useEffect(() => {
    const checkInitialPermissions = async () => {
      try {
        const status = await service.checkPermissions();
        setPermissionStatus(status);

        if (status.granted) {
          // Get last known location first (faster)
          try {
            const lastKnownResult = await service.getLastKnownLocation();
            handleLocationResult(lastKnownResult, true);
          } catch (err) {
            console.error('Hook: getLastKnownLocation error:', err);
          }

          // Then get current location if autoFetch is enabled
          if (autoFetch) {
            setIsLoading(true);
            try {
              const currentResult = await service.getCurrentLocation();
              handleLocationResult(currentResult);
            } catch (err) {
              console.error('Hook: getCurrentLocation error:', err);
            } finally {
              setIsLoading(false);
            }
          }
        }
      } catch (err) {
        console.error('Hook: checkInitialPermissions error:', err);
      }
    };

    checkInitialPermissions();
  }, [service, autoFetch, handleLocationResult]);

  // Handle app state changes
  useEffect(() => {
    if (!fetchOnAppFocus) return;

    const handleAppStateChange = async (nextAppState: AppStateStatus) => {
      // App became active (foreground)
      if (appStateRef.current.match(/inactive|background/) && nextAppState === 'active') {
        if (permissionStatus?.granted) {
          console.log('📱 Uygulama aktif oldu, konum güncelleniyor...');
          setIsLoading(true);
          try {
            const result = await service.getCurrentLocation();
            handleLocationResult(result);
          } catch (err) {
            console.error('Hook: app focus location error:', err);
          } finally {
            setIsLoading(false);
          }
        }
      }
      appStateRef.current = nextAppState;
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);

    return () => {
      subscription?.remove();
    };
  }, [fetchOnAppFocus, permissionStatus?.granted, service, handleLocationResult]);

  // Computed values
  const hasPermission = permissionStatus?.granted ?? false;
  const canRequestPermission = permissionStatus?.canAskAgain ?? true;

  return {
    // State
    location,
    lastKnownLocation,
    error,
    isLoading,
    permissionStatus,

    // Actions
    getCurrentLocation,
    getLastKnownLocation,
    requestPermissions,
    clearError,

    // Computed
    hasPermission,
    canRequestPermission,
  };
}
