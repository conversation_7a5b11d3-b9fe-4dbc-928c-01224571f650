/**
 * Location service types and interfaces
 * Following SOLID principles with clear separation of concerns
 */

import { LocationObject } from 'expo-location';

// Location coordinates interface
export interface LocationCoordinates {
  latitude: number;
  longitude: number;
  altitude?: number;
  accuracy?: number;
  speed?: number;
  heading?: number;
}

// Location data with timestamp
export interface LocationData {
  coordinates: LocationCoordinates;
  timestamp: number;
  mocked?: boolean;
}

// Location permission status
export interface LocationPermissionStatus {
  granted: boolean;
  canAskAgain: boolean;
  status: string;
}

// Location service configuration
export interface LocationServiceConfig {
  accuracy: 'low' | 'balanced' | 'high' | 'highest';
  timeout: number;
  maximumAge: number;
  enableHighAccuracy: boolean;
}

// Location service error types
export enum LocationErrorType {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  POSITION_UNAVAILABLE = 'POSITION_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

// Location service error
export interface LocationError {
  type: LocationErrorType;
  message: string;
  originalError?: Error;
}

// Location service result
export type LocationResult = {
  success: true;
  data: LocationData;
} | {
  success: false;
  error: LocationError;
};

// Location service interface - Single Responsibility Principle
export interface ILocationService {
  getCurrentLocation(): Promise<LocationResult>;
  getLastKnownLocation(): Promise<LocationResult>;
  checkPermissions(): Promise<LocationPermissionStatus>;
  requestPermissions(): Promise<LocationPermissionStatus>;
  isLocationEnabled(): Promise<boolean>;
}

// Location permission handler interface - Interface Segregation Principle
export interface ILocationPermissionHandler {
  checkForegroundPermissions(): Promise<LocationPermissionStatus>;
  requestForegroundPermissions(): Promise<LocationPermissionStatus>;
  openLocationSettings(): Promise<void>;
}

// Location data transformer interface - Single Responsibility Principle
export interface ILocationDataTransformer {
  transformExpoLocation(location: LocationObject): LocationData;
  formatLocationForDisplay(location: LocationData): string;
  formatForConsole(location: LocationData): string;
}

// Location event listener interface - Open/Closed Principle
export interface ILocationEventListener {
  onLocationUpdate(location: LocationData): void;
  onLocationError(error: LocationError): void;
  onPermissionChange(status: LocationPermissionStatus): void;
}

// Location service events
export type LocationServiceEvents = {
  locationUpdate: LocationData;
  locationError: LocationError;
  permissionChange: LocationPermissionStatus;
};
