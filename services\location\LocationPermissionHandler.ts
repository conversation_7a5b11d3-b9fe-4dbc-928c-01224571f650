/**
 * Location Permission Handler
 * Handles all location permission related operations
 * Single Responsibility Principle: Only handles permissions
 */

import * as Location from 'expo-location';
import { Linking } from 'react-native';
import { 
  ILocationPermissionHandler, 
  LocationPermissionStatus, 
  LocationError, 
  LocationErrorType 
} from '../../types/location';

export class LocationPermissionHandler implements ILocationPermissionHandler {
  /**
   * Check current foreground location permissions
   */
  async checkForegroundPermissions(): Promise<LocationPermissionStatus> {
    try {
      const { status, granted, canAskAgain } = await Location.getForegroundPermissionsAsync();
      
      return {
        granted,
        canAskAgain,
        status: status.toString(),
      };
    } catch (error) {
      console.error('Error checking foreground permissions:', error);
      throw this.createPermissionError(error);
    }
  }

  /**
   * Request foreground location permissions
   */
  async requestForegroundPermissions(): Promise<LocationPermissionStatus> {
    try {
      const { status, granted, canAskAgain } = await Location.requestForegroundPermissionsAsync();
      
      return {
        granted,
        canAskAgain,
        status: status.toString(),
      };
    } catch (error) {
      console.error('Error requesting foreground permissions:', error);
      throw this.createPermissionError(error);
    }
  }

  /**
   * Open device location settings
   */
  async openLocationSettings(): Promise<void> {
    try {
      await Linking.openSettings();
    } catch (error) {
      console.error('Error opening location settings:', error);
      throw this.createPermissionError(error);
    }
  }

  /**
   * Create standardized permission error
   */
  private createPermissionError(originalError: unknown): LocationError {
    return {
      type: LocationErrorType.PERMISSION_DENIED,
      message: 'Location permission error occurred',
      originalError: originalError instanceof Error ? originalError : new Error(String(originalError)),
    };
  }
}
