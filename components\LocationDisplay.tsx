/**
 * Location Display Component
 * Displays location information and handles permission requests
 * Follows React component best practices and accessibility guidelines
 */

import React from 'react';
import {
  ActivityIndicator,
  Alert,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { useLocation } from '../hooks/useLocation';
import { LocationDataTransformer } from '../services/location/LocationDataTransformer';

interface LocationDisplayProps {
  showLastKnown?: boolean;
  autoRefresh?: boolean;
  style?: any;
}

export function LocationDisplay({ 
  showLastKnown = true, 
  autoRefresh = true,
  style 
}: LocationDisplayProps) {
  const {
    location,
    lastKnownLocation,
    error,
    isLoading,
    hasPermission,
    canRequestPermission,
    getCurrentLocation,
    requestPermissions,
    clearError,
  } = useLocation({
    autoFetch: autoRefresh,
    fetchOnAppFocus: true,
  });

  const dataTransformer = new LocationDataTransformer();

  // Handle permission request
  const handleRequestPermission = async () => {
    try {
      await requestPermissions();
    } catch (err) {
      console.log(err)
      Alert.alert(
        '<PERSON>zin Hatası',
        'Konum izni alınamadı. Lütfen ayarlardan manuel olarak izin verin.',
        [
          { text: 'Tamam', style: 'default' },
          { 
            text: 'Ayarlara Git', 
            style: 'default',
            onPress: () => {
              // LocationPermissionHandler.openLocationSettings() will be called
              import('../services/location/LocationPermissionHandler').then(({ LocationPermissionHandler }) => {
                const handler = new LocationPermissionHandler();
                handler.openLocationSettings();
              });
            }
          }
        ]
      );
    }
  };

  // Handle manual location refresh
  const handleRefreshLocation = async () => {
    if (!hasPermission) {
      await handleRequestPermission();
      return;
    }
    
    await getCurrentLocation();
  };

  // Handle error display
  const handleErrorPress = () => {
    if (error) {
      Alert.alert(
        'Konum Hatası',
        error.message,
        [
          { text: 'Tamam', onPress: clearError },
          { text: 'Tekrar Dene', onPress: handleRefreshLocation }
        ]
      );
    }
  };

  // Render permission request UI
  if (!hasPermission) {
    return (
      <View style={[styles.container, style]}>
        <View style={styles.permissionContainer}>
          <Text style={styles.permissionTitle}>📍 Konum İzni Gerekli</Text>
          <Text style={styles.permissionMessage}>
            Bu uygulama konum bilginizi almak için izin gerektirir. 
            Namaz vakitlerini doğru hesaplamak için konumunuz kullanılacaktır.
          </Text>
          
          {canRequestPermission ? (
            <TouchableOpacity 
              style={styles.permissionButton}
              onPress={handleRequestPermission}
              accessibilityLabel="Konum izni ver"
              accessibilityHint="Konum izni vermek için dokunun"
            >
              <Text style={styles.permissionButtonText}>İzin Ver</Text>
            </TouchableOpacity>
          ) : (
            <View>
              <Text style={styles.settingsMessage}>
                Konum izni reddedildi. Ayarlardan manuel olarak izin vermeniz gerekiyor.
              </Text>
              <TouchableOpacity 
                style={styles.settingsButton}
                onPress={() => {
                  import('../services/location/LocationPermissionHandler').then(({ LocationPermissionHandler }) => {
                    const handler = new LocationPermissionHandler();
                    handler.openLocationSettings();
                  });
                }}
                accessibilityLabel="Ayarlara git"
                accessibilityHint="Konum ayarlarını açmak için dokunun"
              >
                <Text style={styles.settingsButtonText}>⚙️ Ayarlara Git</Text>
              </TouchableOpacity>
            </View>
          )}
        </View>
      </View>
    );
  }

  return (
    <ScrollView style={[styles.container, style]} showsVerticalScrollIndicator={false}>
      {/* Current Location Section */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>📍 Güncel Konum</Text>
          <TouchableOpacity 
            style={styles.refreshButton}
            onPress={handleRefreshLocation}
            disabled={isLoading}
            accessibilityLabel="Konumu yenile"
            accessibilityHint="Güncel konumu almak için dokunun"
          >
            {isLoading ? (
              <ActivityIndicator size="small" color="#007AFF" />
            ) : (
              <Text style={styles.refreshButtonText}>🔄</Text>
            )}
          </TouchableOpacity>
        </View>
        
        {location ? (
          <View style={styles.locationInfo}>
            <Text style={styles.locationText}>
              {dataTransformer.formatLocationForDisplay(location)}
            </Text>
          </View>
        ) : (
          <Text style={styles.noLocationText}>
            {isLoading ? 'Konum alınıyor...' : 'Konum bilgisi yok'}
          </Text>
        )}
      </View>

      {/* Last Known Location Section */}
      {showLastKnown && lastKnownLocation && (
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>📌 Son Bilinen Konum</Text>
          <View style={styles.locationInfo}>
            <Text style={styles.locationText}>
              {dataTransformer.formatLocationForDisplay(lastKnownLocation)}
            </Text>
          </View>
        </View>
      )}

      {/* Error Section */}
      {error && (
        <TouchableOpacity style={styles.errorContainer} onPress={handleErrorPress}>
          <Text style={styles.errorTitle}>⚠️ Hata</Text>
          <Text style={styles.errorMessage}>{error.message}</Text>
          <Text style={styles.errorHint}>Detaylar için dokunun</Text>
        </TouchableOpacity>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
  },
  section: {
    marginBottom: 20,
    backgroundColor: '#f8f9fa',
    borderRadius: 12,
    padding: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1a1a1a',
  },
  refreshButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#e3f2fd',
  },
  refreshButtonText: {
    fontSize: 16,
  },
  locationInfo: {
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 12,
    borderLeftWidth: 4,
    borderLeftColor: '#4CAF50',
  },
  locationText: {
    fontSize: 14,
    lineHeight: 20,
    color: '#333333',
    fontFamily: 'monospace',
  },
  noLocationText: {
    fontSize: 14,
    color: '#666666',
    fontStyle: 'italic',
    textAlign: 'center',
    padding: 20,
  },
  permissionContainer: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: '#fff3cd',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#ffeaa7',
  },
  permissionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#856404',
    marginBottom: 12,
    textAlign: 'center',
  },
  permissionMessage: {
    fontSize: 14,
    color: '#856404',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 20,
  },
  permissionButton: {
    backgroundColor: '#007AFF',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  permissionButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  settingsMessage: {
    fontSize: 14,
    color: '#856404',
    textAlign: 'center',
    marginBottom: 16,
    lineHeight: 20,
  },
  settingsButton: {
    backgroundColor: '#FF9500',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
  },
  settingsButtonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  errorContainer: {
    backgroundColor: '#f8d7da',
    borderRadius: 8,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#dc3545',
  },
  errorTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#721c24',
    marginBottom: 4,
  },
  errorMessage: {
    fontSize: 14,
    color: '#721c24',
    marginBottom: 4,
  },
  errorHint: {
    fontSize: 12,
    color: '#721c24',
    fontStyle: 'italic',
  },
});
