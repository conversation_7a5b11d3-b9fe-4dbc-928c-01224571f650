/**
 * Location Service Implementation
 * Main service class that orchestrates location operations
 * Dependency Inversion Principle: Depends on abstractions, not concretions
 */

import * as Location from 'expo-location';
import {
  ILocationDataTransformer,
  ILocationPermissionHandler,
  ILocationService,
  LocationError,
  LocationErrorType,
  LocationPermissionStatus,
  LocationResult,
  LocationServiceConfig
} from '../../types/location';

export class LocationService implements ILocationService {
  private config: LocationServiceConfig;

  constructor(
    private permissionHandler: ILocationPermissionHandler,
    private dataTransformer: ILocationDataTransformer,
    config?: Partial<LocationServiceConfig>
  ) {
    // Default configuration - more aggressive for real-time updates
    this.config = {
      accuracy: 'highest', // Use highest accuracy
      timeout: 10000, // Shorter timeout for faster response
      maximumAge: 5000, // Accept only very recent cached locations (5 seconds)
      enableHighAccuracy: true,
      ...config,
    };
  }

  /**
   * Get current device location
   */
  async getCurrentLocation(): Promise<LocationResult> {
    try {
      // Check permissions first
      const permissionStatus = await this.checkPermissions();
      if (!permissionStatus.granted) {
        return {
          success: false,
          error: {
            type: LocationErrorType.PERMISSION_DENIED,
            message: 'Konum izni verilmedi',
          },
        };
      }

      // Check if location services are enabled
      const isEnabled = await this.isLocationEnabled();
      if (!isEnabled) {
        return {
          success: false,
          error: {
            type: LocationErrorType.POSITION_UNAVAILABLE,
            message: 'Konum servisleri devre dışı',
          },
        };
      }

      // Get current location
      const locationOptions = this.getLocationOptions();
      console.log('🔍 Konum alınıyor... Seçenekler:', locationOptions);

      const expoLocation = await Location.getCurrentPositionAsync(locationOptions);

      const locationData = this.dataTransformer.transformExpoLocation(expoLocation);

      // Log to console with more details
      console.log('📍 Güncel konum alındı:', this.dataTransformer.formatForConsole(locationData));
      console.log('🕐 Konum zamanı:', new Date(locationData.timestamp).toLocaleString('tr-TR'));
      console.log('🎯 Doğruluk:', locationData.coordinates.accuracy + 'm');
      console.log('🔄 Sahte konum:', locationData.mocked ? 'Evet' : 'Hayır');

      return {
        success: true,
        data: locationData,
      };
    } catch (error) {
      console.error('Konum alınırken hata:', error);
      return {
        success: false,
        error: this.handleLocationError(error),
      };
    }
  }

  /**
   * Get last known location
   */
  async getLastKnownLocation(): Promise<LocationResult> {
    try {
      const permissionStatus = await this.checkPermissions();
      if (!permissionStatus.granted) {
        return {
          success: false,
          error: {
            type: LocationErrorType.PERMISSION_DENIED,
            message: 'Konum izni verilmedi',
          },
        };
      }

      const expoLocation = await Location.getLastKnownPositionAsync({
        maxAge: this.config.maximumAge,
      });

      if (!expoLocation) {
        return {
          success: false,
          error: {
            type: LocationErrorType.POSITION_UNAVAILABLE,
            message: 'Son bilinen konum bulunamadı',
          },
        };
      }

      const locationData = this.dataTransformer.transformExpoLocation(expoLocation);

      console.log('📍 Son bilinen konum:', this.dataTransformer.formatForConsole(locationData));

      return {
        success: true,
        data: locationData,
      };
    } catch (error) {
      console.error('Son konum alınırken hata:', error);
      return {
        success: false,
        error: this.handleLocationError(error),
      };
    }
  }

  /**
   * Check location permissions
   */
  async checkPermissions(): Promise<LocationPermissionStatus> {
    return await this.permissionHandler.checkForegroundPermissions();
  }

  /**
   * Request location permissions
   */
  async requestPermissions(): Promise<LocationPermissionStatus> {
    return await this.permissionHandler.requestForegroundPermissions();
  }

  /**
   * Check if location services are enabled
   */
  async isLocationEnabled(): Promise<boolean> {
    try {
      return await Location.hasServicesEnabledAsync();
    } catch (error) {
      console.error('Konum servisi durumu kontrol edilemedi:', error);
      return false;
    }
  }

  /**
   * Get location options based on configuration
   */
  private getLocationOptions(): Location.LocationOptions {
    const accuracyMap = {
      low: Location.Accuracy.Low,
      balanced: Location.Accuracy.Balanced,
      high: Location.Accuracy.High,
      highest: Location.Accuracy.Highest,
    };

    return {
      accuracy: accuracyMap[this.config.accuracy],
      // Force fresh location with shorter timeout for more responsive updates
      timeInterval: Math.min(this.config.timeout, 10000), // Max 10 seconds
    };
  }

  /**
   * Handle and standardize location errors
   */
  private handleLocationError(error: unknown): LocationError {
    if (error instanceof Error) {
      if (error.message.includes('timeout')) {
        return {
          type: LocationErrorType.TIMEOUT,
          message: 'Konum alma işlemi zaman aşımına uğradı',
          originalError: error,
        };
      }

      if (error.message.includes('permission')) {
        return {
          type: LocationErrorType.PERMISSION_DENIED,
          message: 'Konum izni reddedildi',
          originalError: error,
        };
      }

      if (error.message.includes('unavailable')) {
        return {
          type: LocationErrorType.POSITION_UNAVAILABLE,
          message: 'Konum servisi kullanılamıyor',
          originalError: error,
        };
      }
    }

    return {
      type: LocationErrorType.UNKNOWN_ERROR,
      message: 'Bilinmeyen konum hatası',
      originalError: error instanceof Error ? error : new Error(String(error)),
    };
  }
}
