{"expo": {"name": "kaza_namazi_takip", "slug": "kaza_namazi_takip", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "kazanamazitakip", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSLocationWhenInUseUsageDescription": "Bu uygulama namaz vakitlerini doğru hesaplamak için konumunuzu kullanır.", "NSLocationAlwaysAndWhenInUseUsageDescription": "Bu uygulama namaz vakitlerini doğru hesaplamak için konumunuzu kullanır."}}, "android": {"adaptiveIcon": {"backgroundColor": "#E6F4FE", "foregroundImage": "./assets/images/android-icon-foreground.png", "backgroundImage": "./assets/images/android-icon-background.png", "monochromeImage": "./assets/images/android-icon-monochrome.png"}, "edgeToEdgeEnabled": true, "predictiveBackGestureEnabled": false}, "web": {"output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff", "dark": {"backgroundColor": "#000000"}}]], "experiments": {"typedRoutes": true, "reactCompiler": true}}}