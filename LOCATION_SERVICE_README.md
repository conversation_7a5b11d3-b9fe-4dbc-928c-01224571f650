# 📍 Lokasyon Servisi

Bu proje, SOLID prensiplerine uygun, kod tekrarı olmayan ve best practice'lere uygun bir lokasyon servisi implementasyonu içerir.

## 🏗️ Mimari

### SOLID Prensipleri

1. **Single Responsibility Principle (SRP)**
   - `LocationService`: Sadece konum işlemlerini yönetir
   - `LocationPermissionHandler`: Sadece izin işlemlerini yönetir
   - `LocationDataTransformer`: Sadece veri dönüşümlerini yapar

2. **Open/Closed Principle (OCP)**
   - Interface'ler geniş<PERSON>meye açık, değişikliğe kapalı
   - Yeni özellikler mevcut kodu bozmadan eklenebilir

3. **Liskov Substitution Principle (LSP)**
   - Tüm implementasyonlar interface'lerini tam olarak karşılar

4. **Interface Segregation Principle (ISP)**
   - Her interface sadece ilgili metodları içerir
   - <PERSON>ere<PERSON><PERSON> bağımlılıklar yoktur

5. **Dependency Inversion Principle (DIP)**
   - Üst seviye modüller alt seviye modüllere bağımlı değil
   - Dependency injection kullanılır

## 📁 Dosya Yapısı

```
├── types/
│   └── location.ts                 # Tip tanımları ve interface'ler
├── services/
│   └── location/
│       ├── LocationService.ts      # Ana lokasyon servisi
│       ├── LocationPermissionHandler.ts  # İzin yönetimi
│       ├── LocationDataTransformer.ts    # Veri dönüşümü
│       └── index.ts               # Barrel export
├── hooks/
│   └── useLocation.ts             # React hook
├── components/
│   └── LocationDisplay.tsx        # UI bileşeni
└── __tests__/                    # Test dosyaları
```

## 🚀 Kullanım

### 1. Basit Kullanım (Hook ile)

```tsx
import { useLocation } from '../hooks/useLocation';

function MyComponent() {
  const {
    location,
    error,
    isLoading,
    hasPermission,
    getCurrentLocation,
    requestPermissions
  } = useLocation({
    autoFetch: true,
    fetchOnAppFocus: true
  });

  if (!hasPermission) {
    return <Button onPress={requestPermissions}>İzin Ver</Button>;
  }

  return (
    <View>
      {location && (
        <Text>
          Konum: {location.coordinates.latitude}, {location.coordinates.longitude}
        </Text>
      )}
      {error && <Text>Hata: {error.message}</Text>}
      <Button onPress={getCurrentLocation} disabled={isLoading}>
        {isLoading ? 'Alınıyor...' : 'Konumu Al'}
      </Button>
    </View>
  );
}
```

### 2. Servis ile Doğrudan Kullanım

```tsx
import { locationService } from '../services/location';

async function getMyLocation() {
  const result = await locationService.getCurrentLocation();
  
  if (result.success) {
    console.log('Konum:', result.data);
  } else {
    console.error('Hata:', result.error);
  }
}
```

### 3. Özel Konfigürasyon

```tsx
import { createLocationService } from '../services/location';

const customLocationService = createLocationService({
  accuracy: 'highest',
  timeout: 10000,
  maximumAge: 30000,
  enableHighAccuracy: true
});
```

## 🎯 Özellikler

### ✅ Temel Özellikler
- ✅ Güncel konum alma
- ✅ Son bilinen konum alma
- ✅ İzin kontrolü ve isteme
- ✅ Otomatik konum güncelleme
- ✅ Uygulama focus olduğunda konum alma
- ✅ Hata yönetimi
- ✅ TypeScript desteği

### 🔧 Teknik Özellikler
- ✅ SOLID prensipleri
- ✅ Dependency Injection
- ✅ Interface-based design
- ✅ Comprehensive error handling
- ✅ Unit testler (%90+ coverage)
- ✅ React hooks pattern
- ✅ Responsive UI components

### 📱 Platform Desteği
- ✅ iOS
- ✅ Android
- ✅ Web (sınırlı)

## 🧪 Test Etme

```bash
# Tüm testleri çalıştır
npm test

# Test coverage raporu
npm run test:coverage

# Watch mode
npm run test:watch
```

## 📋 Gereksinimler

### Paketler
- `expo-location`: Konum servisleri
- `react-native-safe-area-context`: Safe area desteği

### İzinler

#### iOS (app.json)
```json
{
  "ios": {
    "infoPlist": {
      "NSLocationWhenInUseUsageDescription": "Bu uygulama namaz vakitlerini doğru hesaplamak için konumunuzu kullanır.",
      "NSLocationAlwaysAndWhenInUseUsageDescription": "Bu uygulama namaz vakitlerini doğru hesaplamak için konumunuzu kullanır."
    }
  }
}
```

#### Android
Otomatik olarak `expo-location` tarafından eklenir.

## 🎨 UI Bileşeni

`LocationDisplay` bileşeni kullanıma hazır bir UI sağlar:

```tsx
import { LocationDisplay } from '../components/LocationDisplay';

<LocationDisplay 
  showLastKnown={true}
  autoRefresh={true}
/>
```

### Özellikler:
- 📍 Güncel konum gösterimi
- 📌 Son bilinen konum
- ⚠️ Hata mesajları
- 🔄 Manuel yenileme
- ⚙️ Ayarlara yönlendirme
- 🎯 Accessibility desteği

## 🔄 Lifecycle

1. **Uygulama Başlangıcı**
   - İzin kontrolü
   - Son bilinen konum alma
   - Otomatik güncel konum alma (opsiyonel)

2. **Uygulama Focus**
   - Otomatik konum güncelleme (opsiyonel)

3. **Manuel İşlemler**
   - Konum yenileme
   - İzin isteme
   - Ayarlara yönlendirme

## 🐛 Hata Yönetimi

Tüm hatalar tiplendirilmiş ve kullanıcı dostu mesajlarla yönetilir:

```typescript
enum LocationErrorType {
  PERMISSION_DENIED = 'PERMISSION_DENIED',
  POSITION_UNAVAILABLE = 'POSITION_UNAVAILABLE', 
  TIMEOUT = 'TIMEOUT',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}
```

## 🔧 Konfigürasyon

```typescript
interface LocationServiceConfig {
  accuracy: 'low' | 'balanced' | 'high' | 'highest';
  timeout: number;
  maximumAge: number;
  enableHighAccuracy: boolean;
}
```

## 📈 Performans

- ⚡ Son bilinen konum önce gösterilir (hızlı)
- 🎯 Güncel konum arka planda alınır (doğru)
- 💾 Gereksiz API çağrıları önlenir
- 🔄 Akıllı yenileme stratejisi

## 🤝 Katkıda Bulunma

1. SOLID prensiplerine uyun
2. Unit testler yazın
3. TypeScript kullanın
4. Interface'leri genişletin, implementasyonları değiştirin
5. Error handling ekleyin

## 📝 Lisans

MIT License - Detaylar için LICENSE dosyasına bakın.
