/**
 * useLocation Hook Tests
 * Tests for the location hook functionality
 */

import { renderHook, act, waitFor } from '@testing-library/react-native';
import { AppState } from 'react-native';
import { useLocation } from '../../hooks/useLocation';
import { ILocationService, LocationErrorType } from '../../types/location';

// Mock AppState
jest.mock('react-native', () => ({
  AppState: {
    currentState: 'active',
    addEventListener: jest.fn(),
  },
}));

describe('useLocation', () => {
  let mockLocationService: jest.Mocked<ILocationService>;

  const mockLocationData = {
    coordinates: {
      latitude: 41.0082,
      longitude: 28.9784,
      accuracy: 5,
    },
    timestamp: Date.now(),
    mocked: false,
  };

  const mockPermissionStatus = {
    granted: true,
    canAskAgain: true,
    status: 'granted',
  };

  beforeEach(() => {
    mockLocationService = {
      getCurrentLocation: jest.fn(),
      getLastKnownLocation: jest.fn(),
      checkPermissions: jest.fn(),
      requestPermissions: jest.fn(),
      isLocationEnabled: jest.fn(),
    };

    // Setup default mock implementations
    mockLocationService.checkPermissions.mockResolvedValue(mockPermissionStatus);
    mockLocationService.getCurrentLocation.mockResolvedValue({
      success: true,
      data: mockLocationData,
    });
    mockLocationService.getLastKnownLocation.mockResolvedValue({
      success: true,
      data: mockLocationData,
    });
    mockLocationService.requestPermissions.mockResolvedValue(mockPermissionStatus);
    mockLocationService.isLocationEnabled.mockResolvedValue(true);

    // Mock AppState
    (AppState.addEventListener as jest.Mock).mockImplementation((event, callback) => ({
      remove: jest.fn(),
    }));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('initialization', () => {
    it('should initialize with default state', async () => {
      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      expect(result.current.location).toBeNull();
      expect(result.current.lastKnownLocation).toBeNull();
      expect(result.current.error).toBeNull();
      expect(result.current.isLoading).toBe(false);
      expect(result.current.permissionStatus).toBeNull();
      expect(result.current.hasPermission).toBe(false);
      expect(result.current.canRequestPermission).toBe(true);
    });

    it('should check permissions on mount', async () => {
      renderHook(() => useLocation({ service: mockLocationService }));

      await waitFor(() => {
        expect(mockLocationService.checkPermissions).toHaveBeenCalled();
      });
    });

    it('should get last known location when permissions are granted', async () => {
      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await waitFor(() => {
        expect(result.current.permissionStatus?.granted).toBe(true);
        expect(mockLocationService.getLastKnownLocation).toHaveBeenCalled();
      });
    });

    it('should auto-fetch current location when autoFetch is true', async () => {
      const { result } = renderHook(() => 
        useLocation({ 
          service: mockLocationService, 
          autoFetch: true 
        })
      );

      await waitFor(() => {
        expect(result.current.permissionStatus?.granted).toBe(true);
        expect(mockLocationService.getCurrentLocation).toHaveBeenCalled();
      });
    });
  });

  describe('getCurrentLocation', () => {
    it('should get current location successfully', async () => {
      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await act(async () => {
        await result.current.getCurrentLocation();
      });

      await waitFor(() => {
        expect(result.current.location).toEqual(mockLocationData);
        expect(result.current.error).toBeNull();
        expect(result.current.isLoading).toBe(false);
      });
    });

    it('should handle location service errors', async () => {
      const mockError = {
        type: LocationErrorType.TIMEOUT,
        message: 'Timeout error',
      };

      mockLocationService.getCurrentLocation.mockResolvedValue({
        success: false,
        error: mockError,
      });

      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await act(async () => {
        await result.current.getCurrentLocation();
      });

      await waitFor(() => {
        expect(result.current.error).toEqual(mockError);
        expect(result.current.location).toBeNull();
        expect(result.current.isLoading).toBe(false);
      });
    });

    it('should not start new request when already loading', async () => {
      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      // Start first request
      act(() => {
        result.current.getCurrentLocation();
      });

      // Try to start second request while first is loading
      act(() => {
        result.current.getCurrentLocation();
      });

      // Should only call service once
      await waitFor(() => {
        expect(mockLocationService.getCurrentLocation).toHaveBeenCalledTimes(1);
      });
    });
  });

  describe('getLastKnownLocation', () => {
    it('should get last known location successfully', async () => {
      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await act(async () => {
        await result.current.getLastKnownLocation();
      });

      await waitFor(() => {
        expect(result.current.lastKnownLocation).toEqual(mockLocationData);
      });
    });

    it('should handle service errors gracefully', async () => {
      mockLocationService.getLastKnownLocation.mockRejectedValue(new Error('Service error'));

      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await act(async () => {
        await result.current.getLastKnownLocation();
      });

      // Should not crash and should not set lastKnownLocation
      expect(result.current.lastKnownLocation).toBeNull();
    });
  });

  describe('requestPermissions', () => {
    it('should request permissions successfully', async () => {
      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await act(async () => {
        await result.current.requestPermissions();
      });

      await waitFor(() => {
        expect(mockLocationService.requestPermissions).toHaveBeenCalled();
        expect(result.current.permissionStatus).toEqual(mockPermissionStatus);
      });
    });

    it('should auto-fetch location after granting permissions when autoFetch is true', async () => {
      const { result } = renderHook(() => 
        useLocation({ 
          service: mockLocationService, 
          autoFetch: true 
        })
      );

      await act(async () => {
        await result.current.requestPermissions();
      });

      await waitFor(() => {
        expect(mockLocationService.getCurrentLocation).toHaveBeenCalled();
      });
    });

    it('should handle permission request errors', async () => {
      mockLocationService.requestPermissions.mockRejectedValue(new Error('Permission error'));

      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await act(async () => {
        await result.current.requestPermissions();
      });

      await waitFor(() => {
        expect(result.current.error?.type).toBe('PERMISSION_DENIED');
      });
    });
  });

  describe('clearError', () => {
    it('should clear error state', async () => {
      const mockError = {
        type: LocationErrorType.TIMEOUT,
        message: 'Timeout error',
      };

      mockLocationService.getCurrentLocation.mockResolvedValue({
        success: false,
        error: mockError,
      });

      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      // Set error
      await act(async () => {
        await result.current.getCurrentLocation();
      });

      await waitFor(() => {
        expect(result.current.error).toEqual(mockError);
      });

      // Clear error
      act(() => {
        result.current.clearError();
      });

      expect(result.current.error).toBeNull();
    });
  });

  describe('computed values', () => {
    it('should compute hasPermission correctly', async () => {
      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await waitFor(() => {
        expect(result.current.hasPermission).toBe(true);
      });
    });

    it('should compute canRequestPermission correctly', async () => {
      mockLocationService.checkPermissions.mockResolvedValue({
        granted: false,
        canAskAgain: false,
        status: 'denied',
      });

      const { result } = renderHook(() => useLocation({ service: mockLocationService }));

      await waitFor(() => {
        expect(result.current.canRequestPermission).toBe(false);
      });
    });
  });

  describe('app state changes', () => {
    it('should fetch location when app becomes active', async () => {
      let appStateCallback: (state: string) => void;
      
      (AppState.addEventListener as jest.Mock).mockImplementation((event, callback) => {
        if (event === 'change') {
          appStateCallback = callback;
        }
        return { remove: jest.fn() };
      });

      const { result } = renderHook(() => 
        useLocation({ 
          service: mockLocationService, 
          fetchOnAppFocus: true 
        })
      );

      // Wait for initial setup
      await waitFor(() => {
        expect(result.current.hasPermission).toBe(true);
      });

      // Clear previous calls
      jest.clearAllMocks();

      // Simulate app becoming active
      await act(async () => {
        appStateCallback!('active');
      });

      await waitFor(() => {
        expect(mockLocationService.getCurrentLocation).toHaveBeenCalled();
      });
    });

    it('should not fetch location when fetchOnAppFocus is false', async () => {
      let appStateCallback: (state: string) => void;
      
      (AppState.addEventListener as jest.Mock).mockImplementation((event, callback) => {
        if (event === 'change') {
          appStateCallback = callback;
        }
        return { remove: jest.fn() };
      });

      renderHook(() => 
        useLocation({ 
          service: mockLocationService, 
          fetchOnAppFocus: false 
        })
      );

      // Clear previous calls
      jest.clearAllMocks();

      // Simulate app becoming active
      await act(async () => {
        appStateCallback!('active');
      });

      // Should not fetch location
      expect(mockLocationService.getCurrentLocation).not.toHaveBeenCalled();
    });
  });
});
