/**
 * Location Service Tests
 * Comprehensive tests for location service functionality
 */

import { LocationDataTransformer } from '../../services/location/LocationDataTransformer';
import { LocationPermissionHandler } from '../../services/location/LocationPermissionHandler';
import { LocationService } from '../../services/location/LocationService';
import { LocationErrorType } from '../../types/location';

// Mock expo-location
jest.mock('expo-location', () => ({
  getCurrentPositionAsync: jest.fn(),
  getLastKnownPositionAsync: jest.fn(),
  hasServicesEnabledAsync: jest.fn(),
  Accuracy: {
    Low: 1,
    Balanced: 2,
    High: 3,
    Highest: 4,
  },
}));

describe('LocationService', () => {
  let locationService: LocationService;
  let mockPermissionHandler: jest.Mocked<LocationPermissionHandler>;
  let mockDataTransformer: jest.Mocked<LocationDataTransformer>;

  const mockLocationObject = {
    coords: {
      latitude: 41.0082,
      longitude: 28.9784,
      altitude: 100,
      accuracy: 5,
      speed: 0,
      heading: 0,
      altitudeAccuracy: 10,
    },
    timestamp: Date.now(),
    mocked: false,
  };

  const mockLocationData = {
    coordinates: {
      latitude: 41.0082,
      longitude: 28.9784,
      altitude: 100,
      accuracy: 5,
      speed: 0,
      heading: 0,
    },
    timestamp: Date.now(),
    mocked: false,
  };

  beforeEach(() => {
    // Create mocked dependencies
    mockPermissionHandler = {
      checkForegroundPermissions: jest.fn(),
      requestForegroundPermissions: jest.fn(),
      openLocationSettings: jest.fn(),
    } as any;

    mockDataTransformer = {
      transformExpoLocation: jest.fn(),
      formatLocationForDisplay: jest.fn(),
      formatForConsole: jest.fn(),
      calculateDistance: jest.fn(),
    } as any;

    // Create service instance with mocked dependencies
    locationService = new LocationService(
      mockPermissionHandler,
      mockDataTransformer
    );

    // Setup default mock implementations
    mockPermissionHandler.checkForegroundPermissions.mockResolvedValue({
      granted: true,
      canAskAgain: true,
      status: 'granted',
    });

    mockDataTransformer.transformExpoLocation.mockReturnValue(mockLocationData);
    mockDataTransformer.formatForConsole.mockReturnValue('Mock console format');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('getCurrentLocation', () => {
    it('should return location data when permissions are granted', async () => {
      const Location = require('expo-location');
      Location.getCurrentPositionAsync.mockResolvedValue(mockLocationObject);
      Location.hasServicesEnabledAsync.mockResolvedValue(true);

      const result = await locationService.getCurrentLocation();

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(mockLocationData);
      }
      expect(mockPermissionHandler.checkForegroundPermissions).toHaveBeenCalled();
      expect(mockDataTransformer.transformExpoLocation).toHaveBeenCalledWith(mockLocationObject);
    });

    it('should return error when permissions are denied', async () => {
      mockPermissionHandler.checkForegroundPermissions.mockResolvedValue({
        granted: false,
        canAskAgain: true,
        status: 'denied',
      });

      const result = await locationService.getCurrentLocation();

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.type).toBe(LocationErrorType.PERMISSION_DENIED);
        expect(result.error.message).toBe('Konum izni verilmedi');
      }
    });

    it('should return error when location services are disabled', async () => {
      const Location = require('expo-location');
      Location.hasServicesEnabledAsync.mockResolvedValue(false);

      const result = await locationService.getCurrentLocation();

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.type).toBe(LocationErrorType.POSITION_UNAVAILABLE);
        expect(result.error.message).toBe('Konum servisleri devre dışı');
      }
    });

    it('should handle timeout errors', async () => {
      const Location = require('expo-location');
      Location.hasServicesEnabledAsync.mockResolvedValue(true);
      Location.getCurrentPositionAsync.mockRejectedValue(new Error('timeout'));

      const result = await locationService.getCurrentLocation();

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.type).toBe(LocationErrorType.TIMEOUT);
        expect(result.error.message).toBe('Konum alma işlemi zaman aşımına uğradı');
      }
    });
  });

  describe('getLastKnownLocation', () => {
    it('should return last known location when available', async () => {
      const Location = require('expo-location');
      Location.getLastKnownPositionAsync.mockResolvedValue(mockLocationObject);

      const result = await locationService.getLastKnownLocation();

      expect(result.success).toBe(true);
      if (result.success) {
        expect(result.data).toEqual(mockLocationData);
      }
    });

    it('should return error when last known location is not available', async () => {
      const Location = require('expo-location');
      Location.getLastKnownPositionAsync.mockResolvedValue(null);

      const result = await locationService.getLastKnownLocation();

      expect(result.success).toBe(false);
      if (!result.success) {
        expect(result.error.type).toBe(LocationErrorType.POSITION_UNAVAILABLE);
        expect(result.error.message).toBe('Son bilinen konum bulunamadı');
      }
    });
  });

  describe('checkPermissions', () => {
    it('should delegate to permission handler', async () => {
      const expectedStatus = {
        granted: true,
        canAskAgain: true,
        status: 'granted',
      };

      mockPermissionHandler.checkForegroundPermissions.mockResolvedValue(expectedStatus);

      const result = await locationService.checkPermissions();

      expect(result).toEqual(expectedStatus);
      expect(mockPermissionHandler.checkForegroundPermissions).toHaveBeenCalled();
    });
  });

  describe('requestPermissions', () => {
    it('should delegate to permission handler', async () => {
      const expectedStatus = {
        granted: true,
        canAskAgain: true,
        status: 'granted',
      };

      mockPermissionHandler.requestForegroundPermissions.mockResolvedValue(expectedStatus);

      const result = await locationService.requestPermissions();

      expect(result).toEqual(expectedStatus);
      expect(mockPermissionHandler.requestForegroundPermissions).toHaveBeenCalled();
    });
  });

  describe('isLocationEnabled', () => {
    it('should return true when location services are enabled', async () => {
      const Location = require('expo-location');
      Location.hasServicesEnabledAsync.mockResolvedValue(true);

      const result = await locationService.isLocationEnabled();

      expect(result).toBe(true);
    });

    it('should return false when location services are disabled', async () => {
      const Location = require('expo-location');
      Location.hasServicesEnabledAsync.mockResolvedValue(false);

      const result = await locationService.isLocationEnabled();

      expect(result).toBe(false);
    });

    it('should return false when checking services fails', async () => {
      const Location = require('expo-location');
      Location.hasServicesEnabledAsync.mockRejectedValue(new Error('Service check failed'));

      const result = await locationService.isLocationEnabled();

      expect(result).toBe(false);
    });
  });
});
