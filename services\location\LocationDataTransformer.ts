/**
 * Location Data Transformer
 * Transforms and formats location data
 * Single Responsibility Principle: Only handles data transformation
 */

import { LocationObject } from 'expo-location';
import { ILocationDataTransformer, LocationData } from '../../types/location';

export class LocationDataTransformer implements ILocationDataTransformer {
  /**
   * Transform Expo LocationObject to our LocationData format
   */
  transformExpoLocation(location: LocationObject): LocationData {
    return {
      coordinates: {
        latitude: location.coords.latitude,
        longitude: location.coords.longitude,
        altitude: location.coords.altitude || undefined,
        accuracy: location.coords.accuracy || undefined,
        speed: location.coords.speed || undefined,
        heading: location.coords.heading || undefined,
      },
      timestamp: location.timestamp,
      mocked: location.mocked,
    };
  }

  /**
   * Format location data for display
   */
  formatLocationForDisplay(location: LocationData): string {
    const { coordinates, timestamp } = location;
    const date = new Date(timestamp);
    
    const formattedCoords = `${coordinates.latitude.toFixed(6)}, ${coordinates.longitude.toFixed(6)}`;
    const formattedTime = date.toLocaleString('tr-TR');
    
    let displayText = `📍 Konum: ${formattedCoords}\n⏰ Zaman: ${formattedTime}`;
    
    if (coordinates.accuracy) {
      displayText += `\n🎯 Doğruluk: ${coordinates.accuracy.toFixed(0)}m`;
    }
    
    if (coordinates.altitude) {
      displayText += `\n⛰️ Yükseklik: ${coordinates.altitude.toFixed(0)}m`;
    }
    
    if (coordinates.speed && coordinates.speed > 0) {
      displayText += `\n🚀 Hız: ${(coordinates.speed * 3.6).toFixed(1)} km/h`;
    }
    
    if (location.mocked) {
      displayText += `\n⚠️ Sahte konum tespit edildi`;
    }
    
    return displayText;
  }

  /**
   * Format coordinates for console logging
   */
  formatForConsole(location: LocationData): string {
    const { coordinates, timestamp } = location;
    return `[${new Date(timestamp).toISOString()}] Location: ${coordinates.latitude}, ${coordinates.longitude} (±${coordinates.accuracy}m)`;
  }

  /**
   * Calculate distance between two locations (Haversine formula)
   */
  calculateDistance(location1: LocationData, location2: LocationData): number {
    const R = 6371; // Earth's radius in kilometers
    const dLat = this.toRadians(location2.coordinates.latitude - location1.coordinates.latitude);
    const dLon = this.toRadians(location2.coordinates.longitude - location1.coordinates.longitude);
    
    const a = 
      Math.sin(dLat / 2) * Math.sin(dLat / 2) +
      Math.cos(this.toRadians(location1.coordinates.latitude)) * 
      Math.cos(this.toRadians(location2.coordinates.latitude)) * 
      Math.sin(dLon / 2) * Math.sin(dLon / 2);
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
    return R * c; // Distance in kilometers
  }

  /**
   * Convert degrees to radians
   */
  private toRadians(degrees: number): number {
    return degrees * (Math.PI / 180);
  }
}
