/**
 * Location Service Barrel Export
 * Provides a clean API for consuming location services
 * Follows Dependency Injection pattern
 */

import { LocationService } from './LocationService';
import { LocationPermissionHandler } from './LocationPermissionHandler';
import { LocationDataTransformer } from './LocationDataTransformer';
import { LocationServiceConfig } from '../../types/location';

// Factory function to create a configured location service instance
export function createLocationService(config?: Partial<LocationServiceConfig>): LocationService {
  const permissionHandler = new LocationPermissionHandler();
  const dataTransformer = new LocationDataTransformer();
  
  return new LocationService(permissionHandler, dataTransformer, config);
}

// Export all classes for advanced usage
export { LocationService } from './LocationService';
export { LocationPermissionHandler } from './LocationPermissionHandler';
export { LocationDataTransformer } from './LocationDataTransformer';

// Export types
export * from '../../types/location';

// Default service instance
export const locationService = createLocationService();
