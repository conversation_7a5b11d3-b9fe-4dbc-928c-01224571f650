/**
 * Location Data Transformer Tests
 * Tests for location data transformation and formatting
 */

import { LocationObject } from 'expo-location';
import { LocationDataTransformer } from '../../services/location/LocationDataTransformer';

describe('LocationDataTransformer', () => {
  let transformer: LocationDataTransformer;

  beforeEach(() => {
    transformer = new LocationDataTransformer();
  });

  describe('transformExpoLocation', () => {
    it('should transform expo location object correctly', () => {
      const expoLocation: LocationObject = {
        coords: {
          latitude: 41.0082,
          longitude: 28.9784,
          altitude: 100,
          accuracy: 5,
          speed: 10,
          heading: 45,
          altitudeAccuracy: 10,
        },
        timestamp: 1640995200000,
        mocked: false,
      };

      const result = transformer.transformExpoLocation(expoLocation);

      expect(result).toEqual({
        coordinates: {
          latitude: 41.0082,
          longitude: 28.9784,
          altitude: 100,
          accuracy: 5,
          speed: 10,
          heading: 45,
        },
        timestamp: 1640995200000,
        mocked: false,
      });
    });

    it('should handle null values correctly', () => {
      const expoLocation: LocationObject = {
        coords: {
          latitude: 41.0082,
          longitude: 28.9784,
          altitude: null,
          accuracy: null,
          speed: null,
          heading: null,
          altitudeAccuracy: null,
        },
        timestamp: 1640995200000,
        mocked: undefined,
      };

      const result = transformer.transformExpoLocation(expoLocation);

      expect(result.coordinates.altitude).toBeUndefined();
      expect(result.coordinates.accuracy).toBeUndefined();
      expect(result.coordinates.speed).toBeUndefined();
      expect(result.coordinates.heading).toBeUndefined();
    });
  });

  describe('formatLocationForDisplay', () => {
    it('should format location data for display correctly', () => {
      const locationData = {
        coordinates: {
          latitude: 41.008236,
          longitude: 28.978359,
          altitude: 100,
          accuracy: 5,
          speed: 10,
        },
        timestamp: 1640995200000,
        mocked: false,
      };

      const result = transformer.formatLocationForDisplay(locationData);

      expect(result).toContain('📍 Konum: 41.008236, 28.978359');
      expect(result).toContain('⏰ Zaman:');
      expect(result).toContain('🎯 Doğruluk: 5m');
      expect(result).toContain('⛰️ Yükseklik: 100m');
      expect(result).toContain('🚀 Hız: 36.0 km/h');
    });

    it('should handle mocked location', () => {
      const locationData = {
        coordinates: {
          latitude: 41.008236,
          longitude: 28.978359,
        },
        timestamp: 1640995200000,
        mocked: true,
      };

      const result = transformer.formatLocationForDisplay(locationData);

      expect(result).toContain('⚠️ Sahte konum tespit edildi');
    });

    it('should handle missing optional fields', () => {
      const locationData = {
        coordinates: {
          latitude: 41.008236,
          longitude: 28.978359,
        },
        timestamp: 1640995200000,
        mocked: false,
      };

      const result = transformer.formatLocationForDisplay(locationData);

      expect(result).toContain('📍 Konum: 41.008236, 28.978359');
      expect(result).toContain('⏰ Zaman:');
      expect(result).not.toContain('🎯 Doğruluk:');
      expect(result).not.toContain('⛰️ Yükseklik:');
      expect(result).not.toContain('🚀 Hız:');
    });

    it('should not show speed when it is zero', () => {
      const locationData = {
        coordinates: {
          latitude: 41.008236,
          longitude: 28.978359,
          speed: 0,
        },
        timestamp: 1640995200000,
        mocked: false,
      };

      const result = transformer.formatLocationForDisplay(locationData);

      expect(result).not.toContain('🚀 Hız:');
    });
  });

  describe('formatForConsole', () => {
    it('should format location for console logging', () => {
      const locationData = {
        coordinates: {
          latitude: 41.008236,
          longitude: 28.978359,
          accuracy: 5,
        },
        timestamp: 1640995200000,
        mocked: false,
      };

      const result = transformer.formatForConsole(locationData);

      expect(result).toContain('[2022-01-01T00:00:00.000Z]');
      expect(result).toContain('Location: 41.008236, 28.978359');
      expect(result).toContain('(±5m)');
    });

    it('should handle missing accuracy', () => {
      const locationData = {
        coordinates: {
          latitude: 41.008236,
          longitude: 28.978359,
        },
        timestamp: 1640995200000,
        mocked: false,
      };

      const result = transformer.formatForConsole(locationData);

      expect(result).toContain('(±undefinedm)');
    });
  });

  describe('calculateDistance', () => {
    it('should calculate distance between two locations correctly', () => {
      const location1 = {
        coordinates: {
          latitude: 41.0082,
          longitude: 28.9784,
        },
        timestamp: Date.now(),
        mocked: false,
      };

      const location2 = {
        coordinates: {
          latitude: 41.0092,
          longitude: 28.9794,
        },
        timestamp: Date.now(),
        mocked: false,
      };

      const distance = transformer.calculateDistance(location1, location2);

      // Distance should be approximately 0.15 km (150 meters)
      expect(distance).toBeGreaterThan(0.1);
      expect(distance).toBeLessThan(0.2);
    });

    it('should return zero for same location', () => {
      const location = {
        coordinates: {
          latitude: 41.0082,
          longitude: 28.9784,
        },
        timestamp: Date.now(),
        mocked: false,
      };

      const distance = transformer.calculateDistance(location, location);

      expect(distance).toBe(0);
    });

    it('should calculate distance for far locations', () => {
      const istanbul = {
        coordinates: {
          latitude: 41.0082,
          longitude: 28.9784,
        },
        timestamp: Date.now(),
        mocked: false,
      };

      const ankara = {
        coordinates: {
          latitude: 39.9334,
          longitude: 32.8597,
        },
        timestamp: Date.now(),
        mocked: false,
      };

      const distance = transformer.calculateDistance(istanbul, ankara);

      // Distance between Istanbul and Ankara is approximately 350 km
      expect(distance).toBeGreaterThan(300);
      expect(distance).toBeLessThan(400);
    });
  });
});
